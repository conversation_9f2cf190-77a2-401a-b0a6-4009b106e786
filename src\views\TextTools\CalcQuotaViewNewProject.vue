 <!--
      构件指标计算视图组件
      功能：
      1. 显示指标计算结果
      2. 提供指标异常检测功能
    -->
    <template>
        <el-container style="height: 98vh;" v-loading.fullscreen.lock="viewLoading" element-loading-text="加载中...">
            <el-container>
                <!-- 侧边栏容器（带右侧按钮） -->
                <div class="aside-wrapper">
                    <el-aside :width="isCollapse ? '5px' : '220px'" class="aside-container">
                        <el-tree ref="qTree" v-show="!isCollapse" :data="quotaTreeData" highlight-current default-expand-all :expand-on-click-node="false" @current-change="treeChange" node-key="value">
                            <template #default="{ node, data }">
                                <el-text>
                                    {{ node.label }}
                                    <el-text v-if="data.count" :type="data?.type == 2 && data?.relationType == 2 ? 'danger' : ''">({{ data.count}})</el-text>
                                </el-text>
                            </template>
                        </el-tree>
                    </el-aside>
                    <!-- 右侧悬浮按钮 -->
                    <div class="right-collapse-btn" :class="{ 'is-collapsed': isCollapse }" @click="isCollapse=!isCollapse">
                        <Expand v-if="isCollapse" />
                        <Fold v-else />
                    </div>
                </div>
                <!-- 主内容区 -->
                <el-main>
                    <el-space wrap v-loading="viewLoading" element-loading-text="加载中...">
                        <el-card shadow="hover" class="calc-card" v-for="adata in showData" :key="adata.quota_id + '_' + adata.result_index">
                            <div class="calc-tab" :ref="el => setChartRef(el, adata?.quota_id,adata?.result_index,adata?.quota_type)"></div>
                        </el-card>
                    </el-space>
                    <el-empty v-if="!viewLoading && (!showData || showData?.length==0)" description="未检测到指标异常!" />
                </el-main>
            </el-container>
        </el-container>
    </template>
    
    <script>
    import { ref } from 'vue'
    import { VxeTable } from 'vxe-table'
    import Highcharts, { objectEach } from 'highcharts'
    import HighchartsMore from 'highcharts/highcharts-more'
    // import exporting from 'highcharts/modules/exporting'
    import { HoujComponentQuota, GetAllQuota } from '@/api/quota_userProjectResult'
    import { GetGraphicalComponents } from '@/api/graphicalComponent'
    import { ElMessage } from 'element-plus'
    import { groupBy } from 'lodash-es'
    import { Expand, Fold } from '@element-plus/icons-vue'

    Highcharts.setOptions({ lang: { locale: 'zh-CN' } });
    
    export default {
        components: {
            Expand,
            Fold
        },
        data() {
            return {
                viewLoading:true,
                isCollapse: false,
                allComponents: [],
                quotaTreeData: [],
                allQuota: [],
                allData: [],
                showData: [],
                /**
                 * 是否只显示硬指标异常
                 */
                hasHard: true
            }
        },
        async mounted() {
            this.viewLoading=true;
            this.allQuota = await GetAllQuota();
            this.allQuota.forEach(q=>{
                q.jsonData=JSON.parse(q.jsonData);
                q.component_name=q.jsonData.bcomsa[0].bcom;
                q.specialty=q.jsonData.bcomsa[0].bcomIds[0];
            });
            this.viewLoading=false;
            window.addEventListener('message', async (e) => {
                if (e.data.type == 'init') {
                    let userprojectid = e.data.userprojectid;
                    let buildingid = e.data.buildingid;
                    let speciltyids = e.data.speciltyids;
                    let xgj_entnames = e.data.xgj_entnames;
                    let entobjdataids = e.data.entobjdataids;
                    let floornames = e.data.floornames;
                    // 验证必要参数
                    if (!userprojectid || !buildingid) {
                        ElMessage.error('缺少必要参数')
                    }
                    await this.houjquota(userprojectid, buildingid, speciltyids, xgj_entnames, floornames, entobjdataids);
                }
            });
            this.allComponents = await GetGraphicalComponents();
            window.parent.postMessage({type:'mounted'},'*');
        },
        methods: {
            // chartsInit(chart) {
            //     // 动态生成自定义图例
            //     const plotBands = chart.yAxis[0].plotLinesAndBands;
            //     let temp_x = 50; // 图例起始 X 坐标
            //     const legendY = 480; // 图例起始 Y 坐标
            //     plotBands.filter(p=>p?.options?.from).forEach((band, index) => {
            //         temp_x += index*140;
            //         // 创建图例项的颜色块
            //         chart.renderer.rect(temp_x, legendY,18,13)
            //             .attr({
            //                 fill: band.options.color,
            //                 // stroke: 'black',
            //                 // 'stroke-width': 0.5
            //             })
            //             .add();
            //             // .on('click', function () {
            //             //     // 点击图例时切换 plotBand 的显示
            //             //     band.svgElem.attr({ visibility: band.visible ? 'hidden' : 'visible' });
            //             //     band.visible = !band.visible;
            //             // });

            //         // 创建图例项的文本标签
            //         chart.renderer.text(band.options.description, temp_x+23, legendY+10)
            //             .attr({ align: 'left' })
            //             .css({ fontSize: '12px' })
            //             .add();
            //     });
            //     if(chart.quota_multiples){
            //         chart.renderer.rect(temp_x, legendY,18,13).attr({fill: 'red'}).add();
            //         chart.renderer.text(chartOptions.quota_multiples, temp_x+23, legendY+10).attr({ align: 'left' }).css({ fontSize: '12px' }).add();
            //     }
            //     if(chart.quota_fixedValues){
            //         chart.renderer.rect(temp_x, legendY,18,13).attr({fill: 'red'}).add();
            //         chart.renderer.text(chartOptions.quota_fixedValues, temp_x+23, legendY+10).attr({ align: 'left' }).css({ fontSize: '12px' }).add();
            //     }
            // },
            getChartConfig(quota_id,result_index,qtype){
                let temp_result = null;
                let temp_quota = this.allQuota.find(d => d.id == quota_id);
                if (temp_quota) {
                    let results = temp_quota?.jsonData?.results;
                    if (results?.length > 0) temp_result = results[result_index];
                }
                var chartOptions={
                    chart:{ height:'492px'},
                    accessibility: { enabled: false },
                    credits: { enabled: false },
                    exporting: { enabled: false },
                    legend: { enabled: true,layout:'vertical',align:'right',verticalAlign:'middle'},
                    title: { text: null },
                    tooltip:{backgroundColor:'#e2e2e2',color:'black'},
                    subtitle: { text: null }
                };
                if (temp_result) {
                    let colorObj= { 标准: '#ffdc60', 最佳: '#9fe080', 异常: '#ffdc60 ' };
                    let getDesc = (val)=>{
                        if (val > temp_result.result_hard_max || val<temp_result.result_hard_min) return'异常';
                        else if (val > temp_result.result_soft_min && val < temp_result.result_soft_max) return `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        else if (val > temp_result.result_hard_min && val < temp_result.result_hard_max) return `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        else return null;
                    };
                    // let temp_arr = this.allData.flatMap(a => a.quotavalues2).filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //只使用第一个
                    let temp_arr = this.showData.filter(a => a.quota_id == quota_id).flatMap(a => a.all_data);
                    //标题
                    chartOptions.title.text =  temp_quota.title;
                    //自定义副标题
                    chartOptions.subtitle.useHTML=true;
                    chartOptions.subtitle.text='';
                    if(temp_result?.result_soft_min||temp_result?.result_soft_max) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>最佳: ${temp_result?.result_soft_min}~${temp_result?.result_soft_max}</span>`;
                    if(temp_result?.result_hard_min||temp_result?.result_hard_max) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #ffdc60;"></span><span>标准: ${temp_result?.result_hard_min}~${temp_result?.result_hard_max}</span>`;
                    if(temp_result?.multiples>0) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>倍数: ${temp_result?.multiples}</span>`;
                    if(temp_result?.fixedValues?.length>0) chartOptions.subtitle.text+=`<span class="legend-box" style="background-color: #9fe080;"></span><span>固定值: ${temp_result?.fixedValues?.join(',')}</span>`;
                    chartOptions.subtitle.text +=`<span class="legend-box" style="background-color: red;"></span><span>异常</span>`;
                    if(qtype==1){
                        // 柱状图
                        // chartOptions.chart.type='bar';
                        chartOptions.legend={ enabled: true };
                        chartOptions.xAxis={ categories: [], gridLineWidth:1,lineWidth:0 };
                        chartOptions.yAxis={ title: { text: '数量' } };
                        chartOptions.series=[];

                        let descgroup = groupBy(temp_arr.map(d => {let number_val=Number(d.val); return{ val: number_val, des: getDesc(number_val) }}),'des');
                        for(let k of Object.keys(descgroup)){
                            
                            chartOptions.series.push({ type:'bar', name:k, data: descgroup[k].map(s=>s.val), color: colorObj[k.substring(0,2)] });
                        }
                        chartOptions.xAxis.categories = temp_arr.map(d => `楼层: ${d.floorname}`);
                        chartOptions.plotOptions = { bar: { dataLabels: { enabled: true } } };
                    }
                    else if (qtype == 2 && this.hasHard) {
                        // 散点
                        chartOptions.tooltip.formatter = function () {
                            let temp_title = '指标值';//this.series.name;
                            if (this.fixedValues) {
                                temp_title = '非固定值';
                            }
                            else if (this.multiples) {
                                temp_title = '非倍数';
                            }
                            else if (this.noKeyValue) {
                                temp_title = '无数据';
                            }
                            return `楼层: ${this.floorname}  ${temp_title}: ${this.value}`;
                        };

                        chartOptions.xAxis = {
                            gridLineWidth: 0,
                            lineWidth: 0,
                            visible: false,
                            labels: { enabled: false }
                        };
                        chartOptions.yAxis = {
                            title: { text: null },
                            gridLineWidth: 1,
                            // offset: -8,
                            min: 0,
                            // 添加指标值区间带（黄色表示硬指标范围，绿色表示软范围）
                            plotBands: [
                                { color: "#ffdc60", from: temp_result.result_hard_min, to: temp_result.result_hard_max, description: `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})` },
                                { color: "#9fe080", from: temp_result.result_soft_min, to: temp_result.result_soft_max, description: `标准(${temp_result.result_soft_min}~${temp_result.result_soft_max})` },
                            ],
                            plotLines: [{ width: 2, value: temp_result.result_hard_max + 1, color: 'red', dashStyle: 'shortdash' }],
                        };

                        chartOptions.series = [];
                        // 处理指标数据，按值分组并创建散点图系列
                        // temp_arr包含当前选中指标的所有数据点
                        if (temp_arr?.length > 0) {
                            // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                            let temp_group = groupBy(temp_arr, 'val');
                            // 排序方法：数值大的排中间，小的放两边
                            let gkeys = Object.keys(temp_group).sort((a, b) => {
                                const numA = Number(a);
                                const numB = Number(b);
                                const values = Object.keys(temp_group).map(v => Number(v)).sort((a, b) => a - b);
                                const midIndex = Math.floor(values.length / 2);

                                // 计算距离中间的距离，距离越远越靠两边
                                const distA = Math.abs(values.indexOf(numA) - midIndex);
                                const distB = Math.abs(values.indexOf(numB) - midIndex);

                                return distA - distB;
                            });

                            // 计算参考值用于倍数划分
                            const refValue = Math.max(
                                temp_result.result_soft_max || 0,
                                temp_result.result_hard_max || 0,
                                1
                            );
                            let temp_max= refValue * 5;
                            // 定义倍数区间
                            const intervals = [
                                { min: 0, max: refValue , step: 1 },
                                { min: refValue * 2, max: refValue * 5, step: 5},
                                { min: refValue * 3, max: refValue * 10, step: 10,},
                                { min: refValue * 4, max: Infinity, step: 20}
                            ];
                            let temp_series = { type: "scatter", events: { click: this.scatterClick }, marker: { enabledThreshold: 1, radius: 1.2 }, data: [] ,color:'red' }
                            
                            let temp_x = 0;
                            for (let nk of gkeys) {
                                const val = Number(nk);
                                let interval = intervals.find(i => val >= i.min && val < i.max) || intervals[intervals.length - 1];
                                let temp_val=val>temp_max?temp_max:val;
                                let temp_data = temp_group[nk].map((s, i) => {
                                    return {
                                        x: temp_x + i * interval.step,
                                        y: temp_val,
                                        value: temp_val,
                                        floorname: s.floorname,
                                        entobjdataid: s.entobjdataid,
                                        quota_text: s.quota_text
                                    }
                                });
                                temp_series.data.push(temp_data);
                                temp_x += interval.step * temp_group[nk].length;
                            }
                            chartOptions.series.push(temp_series);
                        }
                    }
                    else if(qtype==2){
                        // 极地散点
                        chartOptions.chart.polar=true;
                        chartOptions.tooltip.formatter=function () {
                                let temp_title = '指标值';//this.series.name;
                                if (this.fixedValues) {
                                    temp_title = '非固定值';
                                }
                                else if (this.multiples) {
                                    temp_title = '非倍数';
                                }
                                else if (this.noKeyValue) {
                                    temp_title = '无数据';
                                }
                                return `楼层: ${this.floorname}<br/>${temp_title}: ${this.value}`;
                            };
                        chartOptions.xAxis= {
                            gridLineWidth: 0,
                            lineWidth: 0,
                            labels: { enabled: false }
                        };
                        chartOptions.yAxis= {
                            labels: { enabled: false },
                            gridLineWidth: 0,
                            offset: -8,
                            min: 0,
                            plotBands: [
                                {
                                    from: 0,
                                    to: 270,
                                    color: "#f112125e",
                                },
                                {
                                    from: 50,
                                    to: 200,
                                    color: "#ffdc60",
                                },
                                {
                                    from: 80,
                                    to: 150,
                                    color: "#9fe080",
                                },
                            ],
                            plotLines: [{
                                // label: { text: 'max', y: 4, },
                                value: 240,
                                width: 0.5,
                                //dashStyle: "dashdot"
                            }],
                        };
                        chartOptions.series= [];
                        // 计算最大值（硬指标的1.25倍）
                        let temp_max = temp_result.result_hard_max * 1.25;
                        // 更新图表显示区域配置
                        // 添加指标值区间带（红色表示超出范围，黄色表示软指标范围，绿色表示正常范围）
                        chartOptions.yAxis.plotBands[0].to = temp_max;
                        chartOptions.yAxis.plotBands[0].description = '异常';
                        chartOptions.yAxis.plotBands[1].from = temp_result.result_hard_min;
                        chartOptions.yAxis.plotBands[1].to = temp_result.result_hard_max;
                        chartOptions.yAxis.plotBands[1].description = `标准(${temp_result.result_hard_min}~${temp_result.result_hard_max})`;
                        chartOptions.yAxis.plotBands[2].from = temp_result.result_soft_min;
                        chartOptions.yAxis.plotBands[2].to = temp_result.result_soft_max;
                        chartOptions.yAxis.plotBands[2].description = `最佳(${temp_result.result_soft_min}~${temp_result.result_soft_max})`;
                        chartOptions.yAxis.plotLines[0].value = temp_max+1;
                        //刻度
                        chartOptions.yAxis.tickPositions = [0, temp_result.result_hard_min, temp_result.result_soft_min, temp_result.result_soft_max, temp_result.result_hard_max, temp_max];
                        // 重置图表数据系列
                        chartOptions.series = [];
                        // 处理指标数据，按值分组并创建散点图系列
                        // temp_arr包含当前选中指标的所有数据点
                        if (temp_arr?.length > 0) {
                            // 按指标值对数据进行分组，相同值的数据点会被分到同一组
                            let temp_group = groupBy(temp_arr, 'val');
                            let gkeys=Object.keys(temp_group).sort((a,b)=>Number(a)-Number(b));
                            let errorItems= Object.values(temp_group).map(v=>v[0]).filter(v=>v?.hard||v?.noKeyValue||v?.fixedValues||v?.multiples);
                            let xAvgRatio = 360 / errorItems.length;
                            let temp_xRatio = 0;
                            // 遍历每个不同的指标值
                            for (let nk of gkeys) {
                                // 确保该值组中有数据
                                if (temp_group[nk]?.length > 0) {
                                    // 获取该组的第一个数据点作为代表
                                    let temp_group_data = temp_group[nk][0];
                                    // 判断是否为异常值
                                    let isError = errorItems.find(v=>v.val==nk); //(temp_group_data?.hard || temp_group_data?.noKeyValue || temp_group_data?.fixedValues || temp_group_data?.multiples);
                                    // 计算极坐标图上的角度分布（360度平均分配）
                                    let normalXRatio = 360 / temp_group[nk].length;
                                    let temp_avg_xRatio = xAvgRatio / temp_group[nk].length;
                                    chartOptions.series.push({
                                        type: "scatter",
                                        events:{
                                            click:function(e){
                                                window.parent.postMessage({
                                                    type: 'quota',
                                                    entobjdataid: e.point.options.entobjdataid,
                                                    quota_text:e.point.options.quota_text
                                                }, '*');
                                            }
                                        },
                                       name:`${temp_group_data.val}(${temp_group[nk].length})`,
                                       data:temp_group[nk].map((s, i) => {
                                            return {
                                                floorname: temp_group_data.floorname,
                                                x: isError? (temp_xRatio+ ((i+1)*temp_avg_xRatio)): (i*normalXRatio),
                                                y: (Number(s.val) > temp_max || s.val == null) ? temp_max : Number(s.val),
                                                value: Number(s.val),
                                                // normal:true,
                                                entobjdataid:s.entobjdataid,
                                                quota_text:temp_group_data.quota_text,
                                                fixedValues: temp_group_data.fixedValues,
                                                multiples: temp_group_data.multiples,
                                                noKeyValue: temp_group_data.noKeyValue
                                            }
                                        }),
                                        // color: isNormal ? 'black' : 'red',
                                        marker: { enabledThreshold: 1, radius: isError? 2:1.2 },
                                        color :isError?'red':'black',
                                    });
                                    if(isError){
                                        temp_xRatio += xAvgRatio;
                                    }
                                }
                            }
                        }
                    }
                }
                return chartOptions;
            },
            setChartRef(el, quota_id,result_index,qtype) {
                if (el && quota_id && result_index>-1) {
                    let chartConfig = this.getChartConfig(quota_id,result_index,qtype);
                    Highcharts.chart(el, chartConfig);
                }
            },
            /**
             * 指标数据过滤器
             * @param {Object} d - 指标数据对象
             * @returns {boolean} - 是否为异常数据
             * 异常情况包括：
             * - 无数据(noKeyValue)
             * - 非固定值(fixedValues)
             * - 非倍数值(multiples)
             * - 超出硬指标范围(hard)
             * - 超出软指标范围(soft)
             * 用于筛选需要特殊处理或显示的异常指标数据
             */
            quotaDataFilter(d) {
                return  (d?.hard == true || d?.noKeyValue == true || d?.fixedValues == true || d?.multiples == true);
            },
            /**
             * 指标树节点选择变更处理
             * @param {Object} data - 选中的树节点数据
             */
            treeChange(data) {
                this.viewLoading=true;
                // 根据节点类型显示不同的数据
                if(data.buildname){
                    // 选中建筑节点，显示该建筑下的所有指标数据
                    const buildData = this.allData.find(b => b.buildname === data.buildname);
                    this.showData = buildData ? buildData.quotavalues2 : [];
                }
                else if(data.relationType){
                    // 选中指标节点，显示该指标的数据
                    // 查找包含该指标的所有建筑数据
                    for(let buildData of this.allData) {
                        const quotaData = buildData.quotavalues2.find(q => q.quota_id === data.value);
                        if(quotaData) {
                            this.showData = [quotaData];
                            break;
                        }
                    }
                }
                else if(data.specialty){
                    // 选中专业节点，显示该专业下的所有指标数据
                    // 查找包含该专业的所有建筑数据
                    this.showData = [];
                    for(let buildData of this.allData) {
                        const specialtyData = buildData.quotavalues2.filter(q => q.specialty === data.specialty);
                        this.showData = this.showData.concat(specialtyData);
                    }
                }
                else{
                    // 选中构件节点，显示该构件下的所有指标数据
                    // 查找包含该构件的所有建筑数据
                    this.showData = [];
                    for(let buildData of this.allData) {
                        const componentData = buildData.quotavalues2.filter(q => q.quota.component_name === data.label);
                        this.showData = this.showData.concat(componentData);
                    }
                }
                this.viewLoading=false;
            },
            /**
             * 执行后基础信息表构件指标计算
             * @description 该方法用于处理和计算构件的指标数据
             * @async
             * @function houjquota
             * @returns {Promise<void>}
             * 
             * 主要功能：
             * 1. 清除现有图表数据
             * 2. 重置加载状态和数据结构
             * 3. 获取构件指标数据
             * 4. 处理指标树形结构
             * 5. 展示计算结果
             */
            async houjquota(userprojectid, buildingid, speciltyids, xgj_entnames, floornames, entobjdataids) {
                this.viewLoading = true;
                try {
                    // 重置数据结构
                    this.quotaTreeData = [];
                    
                    // 获取构件指标数据
                    this.allData = await HoujComponentQuota(userprojectid, buildingid, speciltyids, xgj_entnames, floornames, entobjdataids, hasHard);
                    
                    if (this.allData?.length > 0) {
                        // 创建指标ID到指标对象的映射，提高查找效率
                        const quotaMap = new Map(this.allQuota.map(q => [q.id, q]));
                        
                        // 展平数据结构并添加建筑名称
                        let temp_list = this.allData.flatMap(qr => 
                            qr.quotavalues2.map(d => {
                                d.buildname = qr.buildname;
                                // 关联指标定义数据
                                d.quota = quotaMap.get(d.quota_id);
                                return d;
                            })
                        );
                        
                        // 构建树形结构数据
                        const relevantQuotas = this.allQuota.filter(d => 
                            temp_list.some(q => q.quota_id === d.id)
                        );
                        
                        let treeData = relevantQuotas.map(d => {
                            let temp_bcom = d?.jsonData?.bcomsa?.at(0);
                            return {
                                label: d.title,
                                value: d.id,
                                count: 0,
                                type: d.type,
                                relationType: d.relationType,
                                specialty: temp_bcom?.bcomIds?.at(0),
                                bcomid: temp_bcom?.bcomIds?.at(1),
                                bcom: temp_bcom?.bcom
                            };
                        });
                        
                        // 创建treeData的value到对象的映射，提高查找效率
                        const treeDataMap = new Map(treeData.map(td => [td.value, td]));
                        
                        // 计算每个指标的统计数据
                        for (let item of temp_list) {
                            let temp_data = treeDataMap.get(item.quota_id);
                            if (temp_data && item.all_data?.length > 0) {
                                temp_data.count += item.all_data.filter(this.quotaDataFilter).length;
                                // 只设置一次result_index
                                if (temp_data.result_index === undefined) {
                                    temp_data.result_index = item.result_index;
                                }
                            }
                        }
                        
                        // 构建最终的树形结构，使用buildname作为根节点
                        this.quotaTreeData = this.allData.map(buildData => {
                            // 获取当前buildname下的指标数据
                            const buildQuotas = buildData.quotavalues2.map(q => q.quota_id);
                            const buildTreeData = treeData.filter(td => buildQuotas.includes(td.value));
                            
                            return {
                                label: buildData.buildname,
                                value: buildData.buildname,
                                buildname: buildData.buildname,
                                children: this.allComponents
                                    .filter(d => buildTreeData.some(t => t.specialty === d.id))
                                    .map(d => {
                                        const specialtyData = {
                                            label: d.title,
                                            value: d.id,
                                            specialty: d.id,
                                            children: d.children
                                                .filter(c => buildTreeData.some(t => t.bcomid === c.id))
                                                .map(c => {
                                                    return {
                                                        label: c.title,
                                                        value: c.id,
                                                        children: buildTreeData.filter(td => td.bcomid === c.id)
                                                    };
                                                })
                                        };
                                        return specialtyData;
                                    })
                            };
                        });
                        
                        this.showData = this.allData[0].quotavalues2;
                    }
                } catch (error) {
                    console.error('houjquota执行出错:', error);
                    ElMessage.error('数据加载失败');
                } finally {
                    this.viewLoading = false;
                }
            }
        }
    }
    </script>
    
    <style scoped>
    .calc-tab{
        height: 486px;
        width: 500px;
    }
    .el-space{
        display:flex;
    }
    
    /* 树形控件当前节点高亮样式 */
    :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content) {
        background: linear-gradient(90deg, rgba(0, 94, 255, 0.45), rgba(8, 41, 77, 0.12));
        position: relative;
    }
    
    /* 树形控件当前节点高亮样式中的蓝色条 */
    :deep(.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content)::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 5px;
        background-color: #3473fb;
    }
    
    /* 侧边栏容器包装 */
    .aside-wrapper {
      position: relative;
      height: 100%;
      background-color: var(--el-color-info-light-9);
    }
    .aside-wrapper .el-tree{
        background-color: var(--el-color-info-light-9);
    }
    .aside-container {
      height: 100%;
      transition: width 0.3s ease;
      overflow: visible; /* 允许按钮溢出 */
    }
    
    /* 右侧悬浮按钮 */
    .right-collapse-btn {
      position: absolute;
      right: 0px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 48px;
      background: #aaabac;
      border-radius: 12px 0 0 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      cursor: pointer;
      z-index: 10;
      box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    
    /* .right-collapse-btn:hover {
      background: #1f2d3d;
      right: -8px; 
    } */
    
    .right-collapse-btn.is-collapsed {
      right: -12px;
      border-radius: 0 12px 12px 0;
    }
    
    /* 菜单折叠样式 */
    .el-menu--collapse .el-sub-menu__title span {
      display: none;
    }
    
    /* 主内容区动态调整 */
    .el-main {
      transition: margin-left 0.3s ease;
    }
        :deep(.highcharts-subtitle span){
        display: inline-block;
        vertical-align: middle;
    }
    :deep(.highcharts-subtitle span.legend-box){
       width: 13px;
       height: 11px;
    }
    :deep(.highcharts-subtitle span+span.legend-box){
        margin-left: 5px;
    }
     :deep(.highcharts-subtitle span.legend-box+span){
        margin-left: 2px;
    }
    </style>
